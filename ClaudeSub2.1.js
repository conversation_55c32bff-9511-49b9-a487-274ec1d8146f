/**
 * Enhanced Subscription Billing Calendar Automation Script v2.3
 * Manages payment events and reminders in Google Calendar based on Google Sheet data
 *
 * v2.3 CHANGES:
 * - Removed 5-row limit for test status - tests now run all rows
 * - Updated formula logic to match new N column format: =if(B<>"","Payment/"&B&"/"&TEXT(E,"MMM dd yyyy")&"/"&H&"/"&M&"/"&K&"/","")
 * - Removed unused Force Resync options (hash deletion handles sync)
 * - Enhanced out-of-sync detection between N & O columns
 * - Streamlined menu options
 */

// Configuration
const CONFIG = {
  SHEET_NAME: 'Master',
  MAX_ROWS_PER_RUN: 5, // Only applies to active/canceled, not test
  EVENT_COLOR: '6', // Flamingo color
  EVENT_TITLE: 'Payment: {name}, {cost}$ ({owner})',
  REMINDER_TITLE: 'Upcoming Payment: {name} {renewal_date} {cost}$ ({owner})',
  SCRIPT_MARKER: '#SUBSCRIPTIONSCRIPT'
};

// Column mapping - Updated Type to Owner (column M)
const COLUMNS = {
  ROW_ID: 0,             // A
  SUBSCRIPTION_NAME: 1,  // B - This is the actual subscription name (YOUTUBE, NETFLIX, etc.)
  CATEGORY: 2,           // C
  SERVICE_PROVIDER: 3,   // D
  RENEWAL_DATE: 4,       // E
  BILLING_FREQUENCY: 5,  // F
  REMINDER: 6,           // G
  COST: 7,              // H
  PAYMENT_METHOD: 8,     // I
  LOGIN: 9,             // J
  STATUS: 10,           // K
  NOTE: 11,             // L
  OWNER: 12,            // M - Updated from TYPE to OWNER
  LIVE_HASH: 13,        // N - Live hash formula for comparison
  STORED_HASH: 14       // O - For storing the hash/signature
};

// ========== MENU SYSTEM ==========
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('📅 Subscription Manager v2.3')
    .addItem('🔄 Run Sync Now', 'runSyncNow')
    .addSeparator()
    .addSubMenu(ui.createMenu('⚙️ Trigger Settings')
      .addItem('Set Hourly Trigger', 'setHourlyTrigger')
      .addItem('Set Daily Trigger', 'setDailyTrigger')
      .addItem('Set Custom Trigger (4 hours)', 'setCustomTrigger')
      .addItem('Remove All Triggers', 'removeAllTriggers')
      .addItem('Show Current Triggers', 'showCurrentTriggers'))
    .addSeparator()
    .addItem('� Show Processing Status', 'showProcessingStatus')
    .addItem('❓ Help', 'showHelp')
    .addToUi();
}

// Menu Functions - REMOVED SUCCESS POPUPS
function runSyncNow() {
  const ui = SpreadsheetApp.getUi();
  try {
    const result = updateRenewalDatesBatch();
    console.log('✅ Manual sync completed successfully!', result);
    console.log(`📊 Processed ${result.processedCount} active/canceled + ${result.testProcessedCount} test rows out of ${result.totalValidRows} valid rows (${result.totalRows} total rows in sheet)`);
    // REMOVED: Success popup that blocks execution
  } catch (error) {
    console.error('❌ Manual sync failed:', error);
    ui.alert('Error', 'Sync failed: ' + error.toString(), ui.ButtonSet.OK);
  }
}

function setHourlyTrigger() {
  try {
    setupTrigger('HOURLY');
    console.log('✅ Hourly trigger set up successfully!');
    // REMOVED: Success popup
  } catch (error) {
    console.error('❌ Failed to set hourly trigger:', error);
    SpreadsheetApp.getUi().alert('Error', 'Failed to set hourly trigger: ' + error.toString(), SpreadsheetApp.getUi().ButtonSet.OK);
  }
}

function setDailyTrigger() {
  try {
    setupTrigger('DAILY');
    console.log('✅ Daily trigger set up successfully!');
    // REMOVED: Success popup
  } catch (error) {
    console.error('❌ Failed to set daily trigger:', error);
    SpreadsheetApp.getUi().alert('Error', 'Failed to set daily trigger: ' + error.toString(), SpreadsheetApp.getUi().ButtonSet.OK);
  }
}

function setCustomTrigger() {
  try {
    setupTrigger('CUSTOM');
    console.log('✅ 4-hour trigger set up successfully!');
    // REMOVED: Success popup
  } catch (error) {
    console.error('❌ Failed to set custom trigger:', error);
    SpreadsheetApp.getUi().alert('Error', 'Failed to set custom trigger: ' + error.toString(), SpreadsheetApp.getUi().ButtonSet.OK);
  }
}

function removeAllTriggers() {
  try {
    const triggers = ScriptApp.getProjectTriggers();
    let removedCount = 0;
    triggers.forEach(trigger => {
      if (trigger.getHandlerFunction() === 'updateRenewalDatesBatch') {
        ScriptApp.deleteTrigger(trigger);
        removedCount++;
      }
    });
    console.log(`✅ All triggers removed successfully! Removed ${removedCount} triggers.`);
    // REMOVED: Success popup
  } catch (error) {
    console.error('❌ Failed to remove triggers:', error);
    SpreadsheetApp.getUi().alert('Error', 'Failed to remove triggers: ' + error.toString(), SpreadsheetApp.getUi().ButtonSet.OK);
  }
}

function showCurrentTriggers() {
  const triggers = ScriptApp.getProjectTriggers();
  const relevantTriggers = triggers.filter(trigger => trigger.getHandlerFunction() === 'updateRenewalDatesBatch');
  
  if (relevantTriggers.length === 0) {
    SpreadsheetApp.getUi().alert('Trigger Status', 'No active triggers found.', SpreadsheetApp.getUi().ButtonSet.OK);
  } else {
    let message = 'Active Triggers:\n\n';
    relevantTriggers.forEach((trigger, index) => {
      const source = trigger.getTriggerSource();
      const type = trigger.getEventType();
      message += `${index + 1}. Source: ${source}, Type: ${type}\n`;
    });
    SpreadsheetApp.getUi().alert('Trigger Status', message, SpreadsheetApp.getUi().ButtonSet.OK);
  }
}

// REMOVED: Force Resync menu functions - hash changes automatically force sync
// Users can simply delete the hash in column O to force a resync

function showProcessingStatus() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME);
  const data = sheet.getDataRange().getValues();
  
  let activeCount = 0;
  let canceledCount = 0;
  let testCount = 0;
  let needsSyncCount = 0;
  let validRowCount = 0;
  
  // Skip header row and count only rows with subscription names
  for (let i = 1; i < data.length; i++) {
    const subscriptionName = data[i][COLUMNS.SUBSCRIPTION_NAME];
    
    // Only count rows that have a subscription name (skip empty rows)
    if (subscriptionName && subscriptionName.toString().trim() !== '') {
      validRowCount++;
      
      const status = data[i][COLUMNS.STATUS];
      const liveHash = data[i][COLUMNS.LIVE_HASH];
      const storedHash = data[i][COLUMNS.STORED_HASH];
      
      if (status) {
        switch (status.toLowerCase()) {
          case 'active': activeCount++; break;
          case 'canceled': canceledCount++; break;
          case 'test': testCount++; break;
        }
        
        if (liveHash !== storedHash) {
          needsSyncCount++;
        }
      }
    }
  }
  
  const message = `Processing Status:\n\n` +
    `📊 Total Rows in Sheet: ${data.length - 1}\n` +
    `📋 Valid Subscriptions: ${validRowCount}\n` +
    `✅ Active: ${activeCount}\n` +
    `❌ Canceled: ${canceledCount}\n` +
    `🧪 Test: ${testCount}\n` +
    `🔄 Needs Sync: ${needsSyncCount}`;
  
  SpreadsheetApp.getUi().alert('Processing Status', message, SpreadsheetApp.getUi().ButtonSet.OK);
}

function showHelp() {
  const message = `📅 Subscription Manager Help v2.3\n\n` +
    `🔄 Run Sync Now: Process subscriptions that need updates\n` +
    `• Active/Canceled: Max 5 rows per run\n` +
    `• Test: ALL rows processed (no limit)\n\n` +
    `⚙️ Trigger Settings: Configure automatic processing\n` +
    `• Hourly: Process every hour (recommended)\n` +
    `• Daily: Process once per day\n` +
    `• Custom: Process every 4 hours\n\n` +
    `� Processing Status: View current subscription counts\n` +

    `💡 Force Resync: Simply delete the hash in column O\n` +
    `• Hash changes automatically trigger sync\n` +
    `• No manual resync functions needed\n\n` +
    `v2.3 Changes:\n` +
    `• 🧪 Test status: No 5-row limit (processes all)\n` +
    `• � Updated N column formula format\n` +
    `• � Added N & O sync status checker\n` +
    `• 🗑️ Removed unused Force Resync menu options\n` +
    `• ⚡ Simplified workflow\n\n` +
    `The script automatically:\n` +
    `• Creates calendar events for payments\n` +
    `• Sets up reminders (if specified)\n` +
    `• Advances past dates\n` +
    `• Handles status changes\n` +
    `• Skips empty rows in processing`;

  SpreadsheetApp.getUi().alert('Help', message, SpreadsheetApp.getUi().ButtonSet.OK);
}

// ========== MAIN PROCESSING FUNCTIONS ==========

function updateRenewalDatesBatch() {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME);
    if (!sheet) {
      throw new Error(`Sheet "${CONFIG.SHEET_NAME}" not found`);
    }

    const calendar = CalendarApp.getDefaultCalendar();
    const data = sheet.getDataRange().getValues();

    console.log(`📊 Total rows in sheet: ${data.length} (including header)`);

    // Skip header row and filter out empty rows
    const validRows = [];
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      const subscriptionName = row[COLUMNS.SUBSCRIPTION_NAME];

      // Only include rows that have a subscription name
      if (subscriptionName && subscriptionName.toString().trim() !== '') {
        validRows.push({ row: row, originalIndex: i + 1 }); // +1 for 1-based indexing
      }
    }

    console.log(`📋 Valid subscription rows found: ${validRows.length}`);

    let processedCount = 0;
    let testProcessedCount = 0;

    for (let i = 0; i < validRows.length; i++) {
      const { row, originalIndex } = validRows[i];
      const status = row[COLUMNS.STATUS];

      // Check if row needs processing (live hash ≠ stored hash)
      const liveHash = row[COLUMNS.LIVE_HASH];
      const storedHash = row[COLUMNS.STORED_HASH];
      if (liveHash === storedHash) {
        console.log(`⏭️ Skipping row ${originalIndex}: No changes detected`);
        continue;
      }

      // Skip if essential data is missing
      if (!row[COLUMNS.SUBSCRIPTION_NAME] || !row[COLUMNS.RENEWAL_DATE] || !row[COLUMNS.STATUS]) {
        console.log(`⚠️ Skipping row ${originalIndex}: Missing essential data (name, date, or status)`);
        continue;
      }

      // Apply 5-row limit only to active/canceled, not test
      if (status && status.toLowerCase() === 'test') {
        console.log(`🧪 Processing TEST row ${originalIndex}: ${row[COLUMNS.SUBSCRIPTION_NAME]}`);
        processRow(sheet, calendar, row, originalIndex);
        testProcessedCount++;
      } else if (processedCount < CONFIG.MAX_ROWS_PER_RUN) {
        console.log(`🔄 Processing row ${originalIndex}: ${row[COLUMNS.SUBSCRIPTION_NAME]}`);
        processRow(sheet, calendar, row, originalIndex);
        processedCount++;
      } else {
        console.log(`⏸️ Reached max rows limit (${CONFIG.MAX_ROWS_PER_RUN}) for active/canceled. Skipping row ${originalIndex}`);
        break;
      }
    }

    console.log(`✅ Batch processing complete: ${processedCount} active/canceled rows + ${testProcessedCount} test rows processed`);
    return {
      processedCount,
      testProcessedCount,
      totalRows: data.length - 1,
      totalValidRows: validRows.length
    };

  } catch (error) {
    console.error('❌ Error in updateRenewalDatesBatch:', error);
    // Only show error alerts, not success alerts
    SpreadsheetApp.getUi().alert('Error: ' + error.toString());
    throw error;
  }
}

function processRow(sheet, calendar, row, rowIndex) {
  const subscriptionData = {
    rowId: row[COLUMNS.ROW_ID],
    name: row[COLUMNS.SUBSCRIPTION_NAME],
    renewalDate: new Date(row[COLUMNS.RENEWAL_DATE]),
    frequency: row[COLUMNS.BILLING_FREQUENCY] || 'Monthly',
    reminder: row[COLUMNS.REMINDER] || null,
    cost: row[COLUMNS.COST] || 0,
    status: row[COLUMNS.STATUS] || 'active',
    note: row[COLUMNS.NOTE] || '',
    owner: row[COLUMNS.OWNER] || ''
  };

  console.log(`🔍 Processing: ${subscriptionData.name} (Row ${rowIndex}) - Status: ${subscriptionData.status}`);

  // Get current event ID
  let eventId = subscriptionData.rowId;

  // Get the stored hash to check for changes and determine action needed
  const liveHash = row[COLUMNS.LIVE_HASH];
  const storedHash = row[COLUMNS.STORED_HASH];

  console.log(`🔗 Live Hash: ${liveHash}`);
  console.log(`💾 Stored Hash: ${storedHash}`);

  try {
    if (subscriptionData.status.toLowerCase() === 'canceled') {
      // Generate ID if needed for canceled subscriptions
      if (!eventId) {
        eventId = generateEventId(subscriptionData);
        sheet.getRange(rowIndex, COLUMNS.ROW_ID + 1).setValue(eventId);
      }
      handleCanceledSubscription(calendar, subscriptionData, eventId);
      updateStoredHash(sheet, rowIndex, 'Canceled - All future events deleted');

    } else if (subscriptionData.status.toLowerCase() === 'test') {
      // Generate ID if needed for test subscriptions
      if (!eventId) {
        eventId = generateEventId(subscriptionData);
        sheet.getRange(rowIndex, COLUMNS.ROW_ID + 1).setValue(eventId);
      }
      handleTestSubscription(sheet, rowIndex, subscriptionData, eventId, storedHash, liveHash);

    } else if (subscriptionData.status.toLowerCase() === 'active') {
      // The handleActiveSubscription function will handle ID generation internally
      handleActiveSubscription(sheet, calendar, rowIndex, subscriptionData, eventId, storedHash, liveHash);
    }

  } catch (error) {
    console.error(`❌ Error processing ${subscriptionData.name}:`, error);
    updateNote(sheet, rowIndex, `Error: ${error.toString()}`);
  }
}

function handleActiveSubscription(sheet, calendar, rowIndex, subscriptionData, eventId, storedHash, liveHash) {
  // Check if renewal date is in the past and advance if needed
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  let renewalDateUpdated = false;
  if (subscriptionData.renewalDate < today) {
    subscriptionData.renewalDate = advanceRenewalDate(subscriptionData.renewalDate, subscriptionData.frequency);
    // Update the renewal date in the sheet
    sheet.getRange(rowIndex, COLUMNS.RENEWAL_DATE + 1).setValue(subscriptionData.renewalDate);
    renewalDateUpdated = true;
    console.log(`📅 Advanced renewal date for ${subscriptionData.name} to ${subscriptionData.renewalDate}`);
  }

  // Generate unique event ID and store in column A if empty
  if (!eventId) {
    eventId = generateEventId(subscriptionData);
    sheet.getRange(rowIndex, COLUMNS.ROW_ID + 1).setValue(eventId);
    console.log(`🆔 Generated and stored new ID for active subscription: ${eventId}`);
  }

  // The current hash should match the live hash (after potential date update)
  const currentHash = createEventHash(subscriptionData);

  // Always delete old events (including reminders) when hashes don't match
  if (storedHash && storedHash !== currentHash && storedHash !== 'Canceled - All future events deleted') {
    console.log(`🔄 Hash mismatch detected - updating events and reminders`);
    deleteEventsByHash(calendar, storedHash, eventId);
    deleteRemindersByHash(calendar, storedHash, eventId);
  }

  // Always create/update events (this handles both new events and updates)
  createPaymentEvent(calendar, subscriptionData, eventId);

  // Handle reminders - create new ones if specified
  if (subscriptionData.reminder) {
    createReminderEvent(calendar, subscriptionData, eventId);
  }

  // Update stored hash to match current hash
  updateStoredHash(sheet, rowIndex, currentHash);

  // Update sync tracking with event date
  updateSyncStatus(sheet, rowIndex, subscriptionData);
}

function handleTestSubscription(sheet, rowIndex, data, eventId, storedHash, liveHash) {
  // Check if renewal date is in the past and advance if needed (same logic as active)
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  if (data.renewalDate < today) {
    data.renewalDate = advanceRenewalDate(data.renewalDate, data.frequency);
    // Update the renewal date in the sheet even in test mode
    sheet.getRange(rowIndex, COLUMNS.RENEWAL_DATE + 1).setValue(data.renewalDate);
    console.log(`🧪 TEST MODE: Advanced renewal date for ${data.name} to ${data.renewalDate}`);
  }

  const currentHash = createEventHash(data);
  const eventDateStr = Utilities.formatDate(data.renewalDate, Session.getScriptTimeZone(), 'MMM dd, yyyy');

  let preview = `🧪 TEST MODE - Would create: ${CONFIG.EVENT_TITLE
    .replace('{name}', data.name)
    .replace('{cost}', data.cost)
    .replace('{owner}', data.owner)} on ${eventDateStr} | ID: ${eventId}`;

  if (data.reminder) {
    const reminderDate = new Date(data.renewalDate);
    reminderDate.setDate(reminderDate.getDate() - parseInt(data.reminder));
    const reminderDateStr = Utilities.formatDate(reminderDate, Session.getScriptTimeZone(), 'MMM dd, yyyy');
    preview += ` | Reminder: ${reminderDateStr}`;
  }

  // Show what would happen based on hash comparison
  if (!storedHash) {
    preview += ` | Action: CREATE NEW EVENTS`;
  } else if (liveHash !== storedHash) {
    preview += ` | Action: UPDATE EVENTS (delete old, create new)`;
    preview += ` | Old: ${storedHash} | New: ${liveHash}`;
  } else {
    preview += ` | Action: NO CHANGES NEEDED`;
  }

  updateNote(sheet, rowIndex, preview);
  updateStoredHash(sheet, rowIndex, currentHash);

  // Update sync tracking with event date
  updateSyncStatus(sheet, rowIndex, data);
}

function generateEventId(data) {
  // Create timestamp-based unique ID: DDMMYYSUBSCRIPTIONNAME
  const now = new Date();
  const day = String(now.getDate()).padStart(2, '0');
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const year = String(now.getFullYear()).slice(-2);
  const cleanName = data.name.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();

  return `${day}${month}${year}${cleanName}`;
}

function createEventHash(data) {
  // Enhanced hash that includes status and owner (previously type)
  const dateStr = Utilities.formatDate(data.renewalDate, Session.getScriptTimeZone(), 'MMM dd yyyy');
  return `Payment/${data.name}/${dateStr}/${data.cost}/${data.owner}/${data.status}/`;
}

function handleCanceledSubscription(calendar, data, eventId) {
  // Find and delete all future events AND reminders for this subscription
  const today = new Date();
  const futureDate = new Date();
  futureDate.setFullYear(futureDate.getFullYear() + 2); // Look 2 years ahead

  const events = calendar.getEvents(today, futureDate);

  events.forEach(event => {
    const description = event.getDescription();
    if (description && description.includes(CONFIG.SCRIPT_MARKER) &&
        (description.includes(eventId) || description.includes(`${eventId}_reminder`))) {
      event.deleteEvent();
      console.log(`🗑️ Deleted event: ${event.getTitle()}`);
    }
  });
}

function deleteEventsByHash(calendar, oldHash, eventId) {
  // Extract the old date from hash to search for events more efficiently
  const hashParts = oldHash.split('/');
  if (hashParts.length >= 3) {
    try {
      const oldDateStr = hashParts[2]; // "Jul 22 2025" format
      const searchDate = new Date(oldDateStr);

      // Search for events around that date (wider range to be safe)
      const startDate = new Date(searchDate);
      startDate.setDate(startDate.getDate() - 5);
      const endDate = new Date(searchDate);
      endDate.setDate(endDate.getDate() + 5);

      const events = calendar.getEvents(startDate, endDate);

      events.forEach(event => {
        const description = event.getDescription();
        // Delete events that match both the event ID and contain the old hash
        if (description &&
            description.includes(CONFIG.SCRIPT_MARKER) &&
            description.includes(eventId) &&
            description.includes(oldHash) &&
            !description.includes('_reminder')) { // Don't delete reminders here
          event.deleteEvent();
          console.log(`🗑️ Deleted old payment event: ${event.getTitle()}`);
        }
      });
    } catch (error) {
      console.error('❌ Error deleting old events by hash:', error);
    }
  }
}

function deleteRemindersByHash(calendar, oldHash, eventId) {
  // Similar to deleteEventsByHash but specifically for reminders
  const hashParts = oldHash.split('/');
  if (hashParts.length >= 3) {
    try {
      const oldDateStr = hashParts[2];
      const searchDate = new Date(oldDateStr);

      // Search wider range for reminders (they could be weeks before the event)
      const startDate = new Date(searchDate);
      startDate.setDate(startDate.getDate() - 35); // Look up to 35 days before
      const endDate = new Date(searchDate);
      endDate.setDate(endDate.getDate() + 5);

      const events = calendar.getEvents(startDate, endDate);

      events.forEach(event => {
        const description = event.getDescription();
        // Delete reminders that match the event ID and contain the old hash
        if (description &&
            description.includes(CONFIG.SCRIPT_MARKER) &&
            description.includes(`${eventId}_reminder`) &&
            description.includes(oldHash)) {
          event.deleteEvent();
          console.log(`🗑️ Deleted old reminder event: ${event.getTitle()}`);
        }
      });
    } catch (error) {
      console.error('❌ Error deleting old reminders by hash:', error);
    }
  }
}

function createPaymentEvent(calendar, data, eventId) {
  const title = CONFIG.EVENT_TITLE
    .replace('{name}', data.name)
    .replace('{cost}', data.cost)
    .replace('{owner}', data.owner);

  const description = `${CONFIG.SCRIPT_MARKER}\nEvent ID: ${eventId}\nHash: ${createEventHash(data)}`;

  // Check if event already exists with same ID and date
  const existingEvents = calendar.getEventsForDay(data.renewalDate);
  const duplicateEvent = existingEvents.find(event =>
    event.getDescription().includes(eventId) &&
    event.getDescription().includes(CONFIG.SCRIPT_MARKER) &&
    !event.getDescription().includes('_reminder')
  );

  if (!duplicateEvent) {
    const event = calendar.createAllDayEvent(title, data.renewalDate, {description: description});
    event.setColor(CONFIG.EVENT_COLOR);
    console.log(`📅 Created payment event: ${title}`);
  } else {
    console.log(`📅 Payment event already exists: ${title}`);
  }
}

function createReminderEvent(calendar, data, eventId) {
  const reminderDays = parseInt(data.reminder);
  if (isNaN(reminderDays) || reminderDays <= 0) return;

  const reminderDate = new Date(data.renewalDate);
  reminderDate.setDate(reminderDate.getDate() - reminderDays);

  // Don't create reminders for past dates
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  if (reminderDate < today) return;

  const renewalDateStr = Utilities.formatDate(data.renewalDate, Session.getScriptTimeZone(), 'yyyy-MM-dd');
  const title = CONFIG.REMINDER_TITLE
    .replace('{name}', data.name)
    .replace('{renewal_date}', renewalDateStr)
    .replace('{cost}', data.cost)
    .replace('{owner}', data.owner);

  const description = `${CONFIG.SCRIPT_MARKER}\nEvent ID: ${eventId}_reminder\nHash: ${createEventHash(data)}`;

  // Check if reminder already exists
  const existingEvents = calendar.getEventsForDay(reminderDate);
  const duplicateReminder = existingEvents.find(event =>
    event.getDescription().includes(`${eventId}_reminder`) &&
    event.getDescription().includes(CONFIG.SCRIPT_MARKER)
  );

  if (!duplicateReminder) {
    const event = calendar.createAllDayEvent(title, reminderDate, {description: description});
    event.setColor(CONFIG.EVENT_COLOR);
    console.log(`⏰ Created reminder event: ${title}`);
  } else {
    console.log(`⏰ Reminder event already exists: ${title}`);
  }
}

function advanceRenewalDate(currentDate, frequency) {
  const newDate = new Date(currentDate);

  switch (frequency.toLowerCase()) {
    case 'monthly':
      newDate.setMonth(newDate.getMonth() + 1);
      break;
    case 'yearly':
      newDate.setFullYear(newDate.getFullYear() + 1);
      break;
    default:
      console.warn(`⚠️ Unknown frequency: ${frequency}, defaulting to monthly`);
      newDate.setMonth(newDate.getMonth() + 1);
  }

  return newDate;
}

function updateNote(sheet, rowIndex, note) {
  sheet.getRange(rowIndex, COLUMNS.NOTE + 1).setValue(note);
}

function updateStoredHash(sheet, rowIndex, hash) {
  // Store hash with timestamp for sanity check
  const timestamp = new Date();
  const hashWithTimestamp = `${hash} | Updated: ${timestamp.toLocaleString()}`;
  sheet.getRange(rowIndex, COLUMNS.STORED_HASH + 1).setValue(hashWithTimestamp);
}

function extractHashFromStored(storedHashWithTimestamp) {
  // Extract just the hash part from "hash | Updated: timestamp" format
  if (!storedHashWithTimestamp) return '';
  const parts = storedHashWithTimestamp.split(' | Updated:');
  return parts[0] || '';
}

function updateSyncStatus(sheet, rowIndex, data) {
  // Enhanced Live Hash formula that includes status and owner
  const liveHashCell = sheet.getRange(rowIndex, COLUMNS.LIVE_HASH + 1);
  const currentFormula = liveHashCell.getFormula();

  if (!currentFormula) {
    // Updated formula to match user's format: =if(B<>"","Payment/"&B&"/"&TEXT(E,"MMM dd yyyy")&"/"&H&"/"&M&"/"&K&"/","")
    const formula = `=if(B${rowIndex}<>"","Payment/"&B${rowIndex}&"/"&TEXT(E${rowIndex},"MMM dd yyyy")&"/"&H${rowIndex}&"/"&M${rowIndex}&"/"&K${rowIndex}&"/","")`;
    liveHashCell.setFormula(formula);
    console.log(`🔗 Set enhanced live hash formula for row ${rowIndex}`);
  }
}

// Enhanced trigger setup with options
function setupTrigger(type = 'HOURLY') {
  // Delete existing triggers first
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => {
    if (trigger.getHandlerFunction() === 'updateRenewalDatesBatch') {
      ScriptApp.deleteTrigger(trigger);
    }
  });

  // Create new trigger based on type
  let trigger;
  switch (type) {
    case 'HOURLY':
      trigger = ScriptApp.newTrigger('updateRenewalDatesBatch')
        .timeBased()
        .everyHours(1)
        .create();
      break;
    case 'DAILY':
      trigger = ScriptApp.newTrigger('updateRenewalDatesBatch')
        .timeBased()
        .everyDays(1)
        .atHour(9) // 9 AM
        .create();
      break;
    case 'CUSTOM':
      trigger = ScriptApp.newTrigger('updateRenewalDatesBatch')
        .timeBased()
        .everyHours(4)
        .create();
      break;
    default:
      throw new Error('Invalid trigger type');
  }

  console.log(`⚙️ ${type} trigger set up successfully`);
}

// ========== FORCE RESYNC FUNCTIONS - REMOVED ==========
//
// Force resync functions have been removed as they are no longer needed.
// To force a resync, simply delete the hash value in column O (STORED_HASH).
// The script will automatically detect the mismatch and process the row on the next run.
//
// This approach is simpler and more reliable than the previous menu-based functions.
